{"compilerOptions": {"target": "ES2021", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "strictPropertyInitialization": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "useUnknownInCatchVariables": false, "incremental": true, "baseUrl": "src", "downlevelIteration": true, "paths": {"@server/*": ["../server/*"], "@app/*": ["*"]}}, "include": ["next-env.d.ts", "src/**/*.ts", "src/**/*.tsx", "server/types/**/*.d.ts"], "exclude": ["node_modules"]}